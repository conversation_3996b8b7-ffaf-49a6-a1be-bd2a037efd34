import { useState } from 'react'
import AdCard from './AdCard'
import { FaInfoCircle } from 'react-icons/fa'

function AdsList({ ads }) {
  const [selectedAd, setSelectedAd] = useState(null)

  if (!ads || ads.length === 0) {
    return (
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-8 text-center">
        <FaInfoCircle className="mx-auto text-4xl text-gray-400 dark:text-gray-500 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">No ads found</h3>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Try adjusting your search criteria or changing the country.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {ads.map((ad) => (
        <AdCard 
          key={ad.ad_archive_id} 
          ad={ad} 
          isExpanded={selectedAd === ad.ad_archive_id}
          onToggleExpand={() => {
            if (selectedAd === ad.ad_archive_id) {
              setSelectedAd(null)
            } else {
              setSelectedAd(ad.ad_archive_id)
            }
          }}
        />
      ))}
    </div>
  )
}

export default AdsList