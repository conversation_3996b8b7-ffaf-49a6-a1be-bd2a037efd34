import { useState, useEffect, useRef } from "react";
import { FaSearch } from "react-icons/fa";

// Countries list for dropdown
const countries = [
  { code: "GB", name: "United Kingdom" },
  { code: "US", name: "United States" },
  { code: "CA", name: "Canada" },
  { code: "AU", name: "Australia" },
  { code: "DE", name: "Germany" },
  { code: "FR", name: "France" },
];

// Items per page options
const itemsOptions = [10, 20, 50, 100];

function SearchBar({
  darkMode,
  onSearch,
  onPageSelect,
  initialSearchTerm,
  initialCountry,
  initialMaxItems,
}) {
  const [searchQuery, setSearchQuery] = useState(initialSearchTerm || ""); 
  const [country, setCountry] = useState(initialCountry || "GB");
  const [maxItems, setMaxItems] = useState(initialMaxItems || 20);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [selectedPageId, setSelectedPageId] = useState("");

  const debounceRef = useRef(null);
  const suggestionsRef = useRef(null);
  const isSelectingSuggestion = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (searchQuery.trim() === "") {
      setSuggestions([]);
      setShowSuggestions(false);
      setSelectedPageId("");
      setLoadingSuggestions(false);
      return;
    }

    debounceRef.current = setTimeout(() => {
      if (isSelectingSuggestion.current) {
        isSelectingSuggestion.current = false;
        return;
      }

      if (searchQuery.length >= 2) {
        const controller = new AbortController();
        abortControllerRef.current = controller;

        setLoadingSuggestions(true); 
        fetchSuggestions(searchQuery, country, controller.signal);
      }
    }, 1000);

    return () => {
      clearTimeout(debounceRef.current);

      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [searchQuery, country]);

  const fetchSuggestions = async (term, selectedCountry, signal) => {
    if (term.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await onSearch(term, selectedCountry, maxItems, signal); 

      if (response && response.pages && Array.isArray(response.pages)) {
        const formattedSuggestions = response.pages.map((page) => ({
          id: page.page_id,
          name: page.page_name,
        }));

        setSuggestions(formattedSuggestions);
        setShowSuggestions(formattedSuggestions.length > 0);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      if (error.name === "AbortError") {
        return;
      }
      console.error("Error fetching suggestions:", error);
      setSuggestions([]);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Handle suggestion click - show ads for selected page
  const handleSuggestionClick = (suggestion) => {
    // Set flag to prevent API call when setting search query
    isSelectingSuggestion.current = true;

    setSearchQuery(suggestion.name);
    setSelectedPageId(suggestion.id);
    setShowSuggestions(false);

    // Call onPageSelect to show ads for this page
    if (onPageSelect) {
      onPageSelect(suggestion.id, suggestion.name);
    }
  };

  // Handle search input change
  const handleSearchQueryChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle country change
  const handleCountryChange = (e) => {
    setCountry(e.target.value);
  };

  // Handle max items change
  const handleMaxItemsChange = (e) => {
    setMaxItems(Number(e.target.value));
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Single Search Input */}
        <div className="relative" ref={suggestionsRef}>
          <label
            htmlFor="searchQuery"
            className={`block text-sm font-medium ${ darkMode ? "text-white" : "text-gray-700" } mb-1`}
          >
            Search Query
          </label>
          <div className="relative">
            <input
              type="text"
              id="searchQuery"
              className={`input w-full pl-10 ${ darkMode ? "dark:bg-gray-700 dark:text-white" : "bg-white" }`}
              placeholder="e.g. Nike, shoes, technology"
              value={searchQuery}
              onChange={handleSearchQueryChange}
              onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
            />
            <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />

            {/* Loading indicator for suggestions */}
            {loadingSuggestions && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>

          {/* Page Suggestions Dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
              <div className="py-1">
                <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Page Suggestions
                </div>
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.id}
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <div className="flex items-center">
                      <FaSearch className="mr-2 text-gray-400 text-xs" />
                      {suggestion.name}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        <div>
          <label
            htmlFor="country"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Country
          </label>
          <select
            id="country"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={country}
            onChange={handleCountryChange}
          >
            {countries.map((country) => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label
            htmlFor="maxItems"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Results to Fetch
          </label>
          <select
            id="maxItems"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={maxItems}
            onChange={handleMaxItemsChange}
          >
            {itemsOptions.map((option) => (
              <option key={option} value={option}>
                {option} ads
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Instructions */}
      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        <p>
          Type to search and see page suggestions, then click on a page to view
          its ads
        </p>
        {selectedPageId && (
          <p className="text-blue-600 dark:text-blue-400 mt-1">
            ✓ Page selected - showing ads from this page
          </p>
        )}
      </div>
    </div>
  );
}

export default SearchBar;
