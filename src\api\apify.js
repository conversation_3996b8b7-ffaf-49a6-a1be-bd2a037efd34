import axios from "axios";

// Create a single cancellation token source for all requests
let cancelTokenSource = null;

// Backend API configuration
const apiConfig = {
  baseUrl: import.meta.env.VITE_API_URL,
};

/**
 * Fetches ads data from backend server
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @param {number} maxItems - Maximum number of items to fetch
 * @param {string} pageId - Optional page ID for specific advertiser
 * @returns {Promise<Array>} Promise resolving to array of ads
 */
export const fetchAdsData = async (
  searchTerm = "Ecommerce",
  country = "GB",
  maxItems = 20,
  pageId = "",
  signal
) => {
  try {
    console.log(`Fetching ${maxItems} ads for "${searchTerm}" in ${country}...`);

    // Make request to backend API with cancellation token
    const params = {
      query: searchTerm,
      country: country,
      maxItems: maxItems,
    };

    // Add pageId if provided (for specific advertiser search)
    if (pageId) {
      params.pageId = pageId;
    }

    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params,
      signal,
    });

    console.log(
      `Retrieved ${response.data.data.ads.length} ads and ${response.data.data.pages.length} pages from backend`
    );
    return response.data.data; // Returns {ads: [], pages: []}
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log("Request canceled:", error.message);
      return [];
    }

    console.error("Error fetching ads data:", error.message);
    console.warn("Falling back to mock data due to API error");
    // Return mock data in the new format
    return {
      ads: mockData.slice(0, maxItems),
      pages: mockPagesData,
    };
  }
};



// Mock data for development/testing purposes
const mockData = [
  // {
  //   "ad_archive_id": "567233033103915",
  //   "page_id": "427897563737746",
  //   "snapshot": {
  //     "page_name": "Voyd Creative",
  //     "display_format": "CAROUSEL",
  //     "link_url": "http://instagram.com/voyd.creative",
  //     "page_categories": ["Business", "Business", "Business"],
  //     "title": "Voyd Creative"
  //   },
  //   "is_active": true,
  //   "start_date": 1747810800,
  //   "end_date": 1747810800,
  //   "url": "https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=GB&q=ecommerce&search_type=keyword_unordered&media_type=all"
  // }
];

// Mock pages data (matching API response format)
const mockPagesData = [
  { page_id: "576446162439414", page_name: "The Sole Supplier" },
  { page_id: "111287175601856", page_name: "USNKRS" },
  { page_id: "685234997998024", page_name: "bootzone.uk" },
  { page_id: "417602344775535", page_name: "Restored.Snkrs" },
  { page_id: "671925779332867", page_name: "OCC KICKS LTD" },
  { page_id: "601431736386550", page_name: "Kickswow -Shoes" },
];
