import { useState, useEffect } from "react";
import SearchBar from "./SearchBar";
import AdsList from "./AdsList";
import Pagination from "./Pagination";
import { fetchAdsData } from "../api/apify";
import LoadingSpinner from "./LoadingSpinner";
import ErrorMessage from "./ErrorMessage";

function Dashboard({darkMode}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [country, setCountry] = useState("GB");
  const [maxItems, setMaxItems] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [ads, setAds] = useState([]);
  const [itemsPerPage] = useState(5);
  const [hasSearched, setHasSearched] = useState(false);
  const [advertisers, setAdvertisers] = useState([]);
  const [selectedAdvertiser, setSelectedAdvertiser] = useState("");
  const [allAds, setAllAds] = useState([]); // Store all ads before filtering
  const [pages, setPages] = useState([]); // Store pages from API response
  const [selectedPageName, setSelectedPageName] = useState(""); // Store selected page name

  const handleSearch = async (
    term,
    selectedCountry,
    selectedMaxItems,
    signal
  ) => {
    // Only proceed if we have a valid search term
    if (!term || term.trim().length < 2) {
      setHasSearched(false);
      setAds([]);
      setAllAds([]);
      setAdvertisers([]);
      setPages([]);
      setSelectedPageName("");
      setLoading(false);
      return { pages: [] }; // Return empty pages for suggestions
    }

    setSearchTerm(term);
    setCountry(selectedCountry);
    setMaxItems(selectedMaxItems);
    setCurrentPage(1);
    setHasSearched(false); // Don't show ads section until page is selected
    setLoading(true);
    setAds([]); // Clear ads - don't show until page selected
    setAllAds([]);
    setSelectedAdvertiser("");
    setSelectedPageName(""); // Reset selected page

    try {
      setError(null);
      const data = await fetchAdsData(
        term,
        selectedCountry,
        selectedMaxItems,
        "",
        signal
      );

      if (data && data.ads && data.pages) {
        setAllAds(data.ads || []); // Store all ads but don't display
        setPages(data.pages || []);

        // Don't set ads or hasSearched - wait for page selection

        return data; // Return data for suggestions
      }
    } catch (err) {
      console.error("Error loading ads:", err);
      setError("Failed to load ads. Please try again later.");
    } finally {
      setLoading(false);
    }

    return { pages: [] };
  };

  // Handle page selection from suggestions - filter from existing ads
  const handlePageSelect = (pageId, pageName) => {
    setSelectedPageName(pageName);
    setHasSearched(true); // Now show the ads section

    // Filter ads from allAds array for the selected page
    const pageAds = allAds.filter((ad) => ad.page_id === pageId);

    setAds(pageAds);
    setTotalPages(Math.ceil(pageAds.length / itemsPerPage));
    setCurrentPage(1);

    // Extract unique advertisers from the filtered ads
    const uniqueAdvertisers = Array.from(
      new Set(pageAds?.map((ad) => ad.page_id))
    ).map((pageId) => {
      const ad = pageAds.find((ad) => ad.page_id === pageId);
      return {
        id: pageId,
        name: ad?.page_name || "Unknown Advertiser",
      };
    });

    setAdvertisers(uniqueAdvertisers);
    setSelectedAdvertiser(""); // Reset advertiser filter
  };

  // Filter ads when advertiser selection changes
  useEffect(() => {
    if (selectedAdvertiser) {
      const filteredAds = allAds.filter(
        (ad) => ad.page_id === selectedAdvertiser
      );
      setAds(filteredAds);
      setTotalPages(Math.ceil((filteredAds?.length || 0) / itemsPerPage));
      setCurrentPage(1); // Reset to first page when filtering
    } else if (allAds.length > 0) {
      setAds(allAds); // Show all ads when no advertiser is selected
      setTotalPages(Math.ceil((allAds?.length || 0) / itemsPerPage));
    }
  }, [selectedAdvertiser, allAds, itemsPerPage]);

  // Get current ads for pagination
  const indexOfLastAd = currentPage * itemsPerPage;
  const indexOfFirstAd = indexOfLastAd - itemsPerPage;
  const currentAds = ads.slice(indexOfFirstAd, indexOfLastAd);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <div className="space-y-6 animate-fade-in">
      <div className={`${darkMode ? "bg-gray-800" : "bg-white"} rounded-lg shadow-md p-6`}>
        <h2 className={`text-2xl font-bold ${darkMode ? "text-white" : "text-gray-900"} mb-6`}>
          Meta Ads Dashboard
        </h2>
        <SearchBar
          darkMode={darkMode}
          onSearch={handleSearch}
          onPageSelect={handlePageSelect}
          initialSearchTerm={searchTerm}
          initialCountry={country}
          initialMaxItems={maxItems}
        />
      </div>

      {!hasSearched ? (
        <div className={`${darkMode ? "bg-gray-800" : "bg-white"} rounded-lg shadow-md p-6 text-center`}>
          <p className="text-gray-600 dark:text-gray-400">
            Type a search term to see page suggestions, then click on a page to
            view its ads
          </p>
        </div>
      ) : loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorMessage message={error} />
      ) : (
        <>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 md:mb-0">
                {selectedPageName
                  ? `Ads from "${selectedPageName}"`
                  : `Results for "${searchTerm}" in ${country}`}
              </h3>

              {/* Advertiser filter dropdown */}
              {/* {advertisers.length > 0 && (
                <div className="w-full md:w-64">
                  <select
                    className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    value={selectedAdvertiser}
                    onChange={(e) => setSelectedAdvertiser(e.target.value)}
                  >
                    <option value="">All Advertisers</option>
                    {advertisers.map(advertiser => (
                      <option key={advertiser.id} value={advertiser.id}>
                        {advertiser.name}
                      </option>
                    ))}
                  </select>
                </div>
              )} */}
            </div>

            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Showing{" "}
              {ads.length > 0
                ? `${indexOfFirstAd + 1}-${Math.min(indexOfLastAd, ads.length)} of ${ads.length}`
                : "0"}{" "}
              ads
            </p>

            <AdsList ads={currentAds} />

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={paginate}
            />
          </div>
        </>
      )}
    </div>
  );
}

export default Dashboard;
