import { Fa<PERSON>oon, FaSun } from 'react-icons/fa'
import { BsMeta } from "react-icons/bs";

function Header({ darkMode, toggleDarkMode }) {
  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <BsMeta className="text-primary-600 text-3xl" />
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">Meta Ads Explorer</h1>
          </div>
          
          <button
            onClick={toggleDarkMode}
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
          >
            {darkMode ? (
              <FaSun className="text-yellow-400 text-xl" />
            ) : (
              <FaMoon className="text-gray-700 text-xl" />
            )}
          </button>
        </div>
      </div>
    </header>
  )
}

export default Header