import { useState } from 'react'
import { FaChevronDown, FaChevronUp, FaExternalLinkAlt, FaImage, FaVideo } from 'react-icons/fa'

function AdCard({ ad, isExpanded, onToggleExpand }) {
  const [activeTab, setActiveTab] = useState('overview')
  
  if (!ad) return null

  const hasImages = ad.snapshot?.images?.length > 0 || ad.snapshot?.cards?.some(card => card.resized_image_url)
  const hasVideos = ad.snapshot?.videos?.length > 0
  const hasCards = ad.snapshot?.cards?.length > 0

  // Format date
  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A'
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className={`card bg-black/10 transition-all duration-300 ${isExpanded ? 'ring-2 ring-primary-500' : ''}`}>
      <div className="p-4 sm:p-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            {ad.snapshot?.page_profile_picture_url && (
              <img 
                src={ad.snapshot.page_profile_picture_url} 
                alt={ad.page_name || 'Advertiser'} 
                className="w-12 h-12 rounded-full object-cover"
              />
            )}
            <div>
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                {ad.page_name || 'Unknown Advertiser'}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {ad.snapshot?.display_format || 'Ad'} • ID: {ad.ad_archive_id?.substring(0, 8)}...
              </p>
            </div>
          </div>
          
          <button 
            onClick={onToggleExpand}
            className="p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label={isExpanded ? "Collapse ad details" : "Expand ad details"}
          >
            {isExpanded ? <FaChevronUp /> : <FaChevronDown />}
          </button>
        </div>
        
        <div className="mt-4">
          {ad.snapshot?.body?.text && ad.snapshot.body.text !== "{{product.brand}}" && (
            <p className="text-gray-700 dark:text-gray-300 line-clamp-3">
              {ad.snapshot.body.text}
            </p>
          )}
        </div>
        
        {hasImages && !isExpanded && (
          <div className="mt-4 flex overflow-x-auto space-x-2 pb-2">
            {ad.snapshot.images?.map((image, index) => (
              <img 
                key={index}
                src={image.resized_image_url || image.original_image_url} 
                alt="Ad creative"
                className="h-24 w-auto object-cover rounded"
              />
            ))}
            {ad.snapshot.cards?.filter(card => card.resized_image_url).map((card, index) => (
              <img 
                key={`card-${index}`}
                src={card.resized_image_url} 
                alt="Ad creative"
                className="h-24 w-auto object-cover rounded"
              />
            ))}
          </div>
        )}
        
        {hasVideos && !isExpanded && (
          <div className="mt-4 flex items-center text-primary-600">
            <FaVideo className="mr-2" />
            <span>This ad contains video content</span>
          </div>
        )}

        <div className="mt-4 flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
          <div>
            Start: {formatDate(ad.start_date)} • End: {formatDate(ad.end_date)}
          </div>
          
          {ad.url && (
            <a 
              href={ad.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center text-primary-600 hover:text-primary-700 transition-colors"
            >
              View on Facebook <FaExternalLinkAlt className="ml-1" />
            </a>
          )}
        </div>
      </div>
      
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 animate-fade-in">
          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex overflow-x-auto">
              <button
                className={`py-4 px-6 font-medium text-sm border-b-2 ${
                  activeTab === 'overview' 
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              
              {hasCards && (
                <button
                  className={`py-4 px-6 font-medium text-sm border-b-2 ${
                    activeTab === 'cards' 
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                  onClick={() => setActiveTab('cards')}
                >
                  Cards ({ad.snapshot.cards.length})
                </button>
              )}
              
              {(hasImages || hasVideos) && (
                <button
                  className={`py-4 px-6 font-medium text-sm border-b-2 ${
                    activeTab === 'media' 
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                  onClick={() => setActiveTab('media')}
                >
                  Media
                </button>
              )}
              
              <button
                className={`py-4 px-6 font-medium text-sm border-b-2 ${
                  activeTab === 'json' 
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('json')}
              >
                JSON Data
              </button>
            </nav>
          </div>
          
          {/* Tab content */}
          <div className="p-4 sm:p-6">
            {activeTab === 'overview' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Advertiser Details</h4>
                    <dl className="mt-2 space-y-1">
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Name:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{ad.page_name || 'N/A'}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">ID:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{ad.page_id || 'N/A'}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Type:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{ad.entity_type || 'N/A'}</dd>
                      </div>
                      {ad.advertiser?.page?.about?.text && (
                        <div className="flex">
                          <dt className="w-1/3 text-gray-500 dark:text-gray-400">About:</dt>
                          <dd className="w-2/3 text-gray-900 dark:text-white">{ad.advertiser.page.about.text}</dd>
                        </div>
                      )}
                    </dl>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Ad Details</h4>
                    <dl className="mt-2 space-y-1">
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Archive ID:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{ad.ad_archive_id || 'N/A'}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Format:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{ad.snapshot?.display_format || 'N/A'}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Start Date:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{formatDate(ad.start_date)}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">End Date:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">{formatDate(ad.end_date)}</dd>
                      </div>
                      <div className="flex">
                        <dt className="w-1/3 text-gray-500 dark:text-gray-400">Active:</dt>
                        <dd className="w-2/3 text-gray-900 dark:text-white">
                          {ad.is_active ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Inactive
                            </span>
                          )}
                        </dd>
                      </div>
                    </dl>
                  </div>
                </div>
                
                {/* Add spending and reach information */}
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Spending & Reach</h4>
                  <dl className="mt-2 flex flex-col gap-x-4 gap-y-2 ">
                      <div className='flex gap-2 items-center'>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Ad Spending:</dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {ad?.spend || "Not Available"}
                        </dd>
                      </div>
                      {/* <div className='flex gap-2 items-center'>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Estimated Reach:</dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {ad?.estimated_reach?.toLocaleString() || "Not Available"}
                        </dd>
                      </div> */}

                      <div className='flex gap-2 items-center'>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">aaa_info Eligible:</dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {ad?.is_aaa_eligible ? "Yes" : "No"}
                        </dd>
                      </div>
                      <div className='flex gap-2 items-center'>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">EU Total Reach:</dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {ad?.aaa_info?.eu_total_reach || "Not Available"}
                        </dd>
                      </div>
                      
                  </dl>
                </div>
                
                {ad.snapshot?.body?.text && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Ad Copy</h4>
                    <p className="mt-2 text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      {ad.snapshot.body.text}
                    </p>
                  </div>
                )}
                
                {ad.snapshot?.link_url && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Destination URL</h4>
                    <a 
                      href={ad.snapshot.link_url} 
                      target="_blank"
                      rel="noopener noreferrer" 
                      className="mt-2 inline-block text-primary-600 hover:underline break-all"
                    >
                      {ad.snapshot.link_url}
                    </a>
                  </div>
                )}
                
                {ad.publisher_platform && ad.publisher_platform.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Platforms</h4>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {ad.publisher_platform.map((platform, index) => (
                        <span 
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        >
                          {platform}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'cards' && ad.snapshot?.cards && (
              <div className="space-y-6">
                {ad.snapshot.cards.map((card, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-white">Card {index + 1}</h4>
                    </div>
                    
                    <div className="p-4">
                      {card.resized_image_url && (
                        <div className="mb-4">
                          <img 
                            src={card.resized_image_url} 
                            alt={`Card ${index + 1}`}
                            className="max-h-64 mx-auto rounded"
                          />
                        </div>
                      )}
                      
                      <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                        {card.title && (
                          <div className="sm:col-span-2">
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{card.title}</dd>
                          </div>
                        )}
                        
                        {card.body && (
                          <div className="sm:col-span-2">
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Body</dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{card.body}</dd>
                          </div>
                        )}
                        
                        {card.link_description && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{card.link_description}</dd>
                          </div>
                        )}
                        
                        {card.caption && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Caption</dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{card.caption}</dd>
                          </div>
                        )}
                        
                        {card.cta_text && (
                          <div>
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">CTA</dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">{card.cta_text}</dd>
                          </div>
                        )}
                        
                        {card.link_url && (
                          <div className="sm:col-span-2">
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">URL</dt>
                            <dd className="mt-1 text-sm text-primary-600 break-all">
                              <a href={card.link_url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                {card.link_url}
                              </a>
                            </dd>
                          </div>
                        )}
                      </dl>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {activeTab === 'media' && (
              <div className="space-y-6">
                {hasImages && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-4">Images</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {ad.snapshot.images?.map((image, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          <img 
                            src={image.resized_image_url || image.original_image_url} 
                            alt={`Ad image ${index + 1}`}
                            className="w-full h-48 object-cover"
                          />
                        </div>
                      ))}
                      
                      {ad.snapshot.cards?.filter(card => card.resized_image_url).map((card, index) => (
                        <div key={`card-img-${index}`} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          <img 
                            src={card.resized_image_url} 
                            alt={`Card image ${index + 1}`}
                            className="w-full h-48 object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {hasVideos && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-4">Videos</h4>
                    <div className="grid grid-cols-1 gap-4">
                      {ad.snapshot.videos?.map((video, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                          {video.video_sd_url ? (
                            <video 
                              controls
                              poster={video.video_preview_image_url}
                              className="w-full"
                            >
                              <source src={video.video_hd_url || video.video_sd_url} type="video/mp4" />
                              Your browser does not support the video tag.
                            </video>
                          ) : (
                            <div className="bg-gray-100 dark:bg-gray-800 p-4 text-center">
                              <FaVideo className="mx-auto text-4xl text-gray-400 mb-2" />
                              <p className="text-gray-500">Video preview not available</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {!hasImages && !hasVideos && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-8 text-center">
                    <FaImage className="mx-auto text-4xl text-gray-400 dark:text-gray-500 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">No media found</h3>
                    <p className="mt-2 text-gray-600 dark:text-gray-400">
                      This ad doesn't contain any images or videos.
                    </p>
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'json' && (
              <div className="overflow-auto">
                <pre className="text-xs bg-gray-50 text-gray-200 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto max-h-96">
                  {JSON.stringify(ad, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default AdCard
